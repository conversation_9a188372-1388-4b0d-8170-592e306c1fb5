#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步vs同步浏览器性能对比测试
"""

import asyncio
import socket
import json
import time
import threading
from concurrent.futures import ThreadPoolExecutor


async def send_command_async(command, config_variant=None, host='localhost', port=8889):
    """异步发送命令"""
    try:
        reader, writer = await asyncio.open_connection(host, port)
        
        data = {
            "command": command,
            "ConfigVariant": config_variant or {}
        }
        
        start_time = time.time()
        
        # 发送数据
        message = json.dumps(data).encode('utf-8')
        writer.write(message)
        await writer.drain()
        
        # 接收响应
        response_data = await reader.read(4096)
        response = response_data.decode('utf-8')
        
        end_time = time.time()
        
        # 关闭连接
        writer.close()
        await writer.wait_closed()
        
        return response, end_time - start_time
        
    except Exception as e:
        print(f"异步发送命令失败: {e}")
        return None, None


def send_command_sync(command, config_variant=None, host='localhost', port=8888):
    """同步发送命令"""
    try:
        client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        client_socket.settimeout(30)
        client_socket.connect((host, port))
        
        data = {
            "command": command,
            "ConfigVariant": config_variant or {}
        }
        
        start_time = time.time()
        client_socket.send(json.dumps(data).encode('utf-8'))
        response = client_socket.recv(4096).decode('utf-8')
        end_time = time.time()
        
        client_socket.close()
        
        return response, end_time - start_time
        
    except Exception as e:
        print(f"同步发送命令失败: {e}")
        return None, None


async def test_async_performance():
    """测试异步版本性能"""
    print("=== 测试异步版本性能 ===")
    
    commands = ['wifi_page', 'system_page', 'preview_page']
    times = []
    
    print("1. 单个命令测试:")
    for cmd in commands:
        print(f"测试命令: {cmd}")
        response, response_time = await send_command_async(cmd)
        if response and response_time:
            times.append(response_time)
            print(f"  响应时间: {response_time:.3f}秒")
            if "成功" in response:
                print(f"  ✓ {cmd} 执行成功")
            else:
                print(f"  ✗ {cmd} 执行失败")
        await asyncio.sleep(1)
    
    print("\n2. 并发命令测试:")
    start_time = time.time()
    
    # 并发执行所有命令
    tasks = [send_command_async(cmd) for cmd in commands]
    results = await asyncio.gather(*tasks)
    
    end_time = time.time()
    concurrent_time = end_time - start_time
    
    print(f"并发执行时间: {concurrent_time:.3f}秒")
    
    # 统计结果
    if times:
        avg_time = sum(times) / len(times)
        print(f"\n异步版本统计:")
        print(f"  平均响应时间: {avg_time:.3f}秒")
        print(f"  最快响应时间: {min(times):.3f}秒")
        print(f"  最慢响应时间: {max(times):.3f}秒")
        print(f"  并发执行时间: {concurrent_time:.3f}秒")
        return avg_time, concurrent_time
    
    return None, None


def test_sync_performance():
    """测试同步版本性能"""
    print("\n=== 测试同步版本性能 ===")
    
    commands = ['wifi_page', 'system_page', 'preview_page']
    times = []
    
    print("1. 单个命令测试:")
    for cmd in commands:
        print(f"测试命令: {cmd}")
        response, response_time = send_command_sync(cmd)
        if response and response_time:
            times.append(response_time)
            print(f"  响应时间: {response_time:.3f}秒")
            if "成功" in response:
                print(f"  ✓ {cmd} 执行成功")
            else:
                print(f"  ✗ {cmd} 执行失败")
        time.sleep(1)
    
    print("\n2. 并发命令测试:")
    start_time = time.time()
    
    # 使用线程池并发执行
    with ThreadPoolExecutor(max_workers=3) as executor:
        futures = [executor.submit(send_command_sync, cmd) for cmd in commands]
        results = [future.result() for future in futures]
    
    end_time = time.time()
    concurrent_time = end_time - start_time
    
    print(f"并发执行时间: {concurrent_time:.3f}秒")
    
    # 统计结果
    if times:
        avg_time = sum(times) / len(times)
        print(f"\n同步版本统计:")
        print(f"  平均响应时间: {avg_time:.3f}秒")
        print(f"  最快响应时间: {min(times):.3f}秒")
        print(f"  最慢响应时间: {max(times):.3f}秒")
        print(f"  并发执行时间: {concurrent_time:.3f}秒")
        return avg_time, concurrent_time
    
    return None, None


async def test_window_control_async():
    """测试异步版本的窗口控制"""
    print("\n=== 测试异步版本窗口控制 ===")
    
    # 测试窗口控制命令
    window_commands = [
        'show_browser',
        'hide_browser',
        ('set_auto_hide', {'delay': '5'}),
        'cancel_auto_hide'
    ]
    
    for cmd in window_commands:
        if isinstance(cmd, tuple):
            command, config = cmd
            print(f"测试命令: {command} with config: {config}")
            response, response_time = await send_command_async(command, config)
        else:
            print(f"测试命令: {cmd}")
            response, response_time = await send_command_async(cmd)
        
        if response and response_time:
            print(f"  响应时间: {response_time:.3f}秒")
            if "成功" in response or "已" in response:
                print(f"  ✓ {cmd} 执行成功")
            else:
                print(f"  ✗ {cmd} 执行失败")
        
        await asyncio.sleep(1)


def test_window_control_sync():
    """测试同步版本的窗口控制"""
    print("\n=== 测试同步版本窗口控制 ===")
    
    # 测试窗口控制命令
    window_commands = [
        'show_browser',
        'hide_browser',
        ('set_auto_hide', {'delay': '5'}),
        'cancel_auto_hide'
    ]
    
    for cmd in window_commands:
        if isinstance(cmd, tuple):
            command, config = cmd
            print(f"测试命令: {command} with config: {config}")
            response, response_time = send_command_sync(command, config)
        else:
            print(f"测试命令: {cmd}")
            response, response_time = send_command_sync(cmd)
        
        if response and response_time:
            print(f"  响应时间: {response_time:.3f}秒")
            if "成功" in response or "已" in response:
                print(f"  ✓ {cmd} 执行成功")
            else:
                print(f"  ✗ {cmd} 执行失败")
        
        time.sleep(1)


async def main():
    """主测试函数"""
    print("浏览器性能对比测试")
    print("请确保两个服务器都已启动:")
    print("- 同步服务器: 端口8888")
    print("- 异步服务器: 端口8889")
    print()
    
    # 检查服务器连接
    try:
        # 检查同步服务器
        test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        test_socket.connect(('localhost', 8888))
        test_socket.close()
        print("✓ 同步服务器连接正常")
    except:
        print("✗ 同步服务器连接失败")
        return
    
    try:
        # 检查异步服务器
        reader, writer = await asyncio.open_connection('localhost', 8889)
        writer.close()
        await writer.wait_closed()
        print("✓ 异步服务器连接正常")
    except:
        print("✗ 异步服务器连接失败")
        return
    
    print()
    
    # 运行性能测试
    async_avg, async_concurrent = await test_async_performance()
    sync_avg, sync_concurrent = test_sync_performance()
    
    # 运行窗口控制测试
    await test_window_control_async()
    test_window_control_sync()
    
    # 性能对比总结
    print("\n" + "="*50)
    print("性能对比总结")
    print("="*50)
    
    if async_avg and sync_avg:
        print(f"平均响应时间:")
        print(f"  异步版本: {async_avg:.3f}秒")
        print(f"  同步版本: {sync_avg:.3f}秒")
        
        if async_avg < sync_avg:
            improvement = ((sync_avg - async_avg) / sync_avg) * 100
            print(f"  ✓ 异步版本快 {improvement:.1f}%")
        else:
            degradation = ((async_avg - sync_avg) / sync_avg) * 100
            print(f"  ✗ 异步版本慢 {degradation:.1f}%")
    
    if async_concurrent and sync_concurrent:
        print(f"\n并发执行时间:")
        print(f"  异步版本: {async_concurrent:.3f}秒")
        print(f"  同步版本: {sync_concurrent:.3f}秒")
        
        if async_concurrent < sync_concurrent:
            improvement = ((sync_concurrent - async_concurrent) / sync_concurrent) * 100
            print(f"  ✓ 异步版本快 {improvement:.1f}%")
        else:
            degradation = ((async_concurrent - sync_concurrent) / sync_concurrent) * 100
            print(f"  ✗ 异步版本慢 {degradation:.1f}%")
    
    print(f"\n异步版本优势:")
    print(f"  ✓ 避免greenlet线程冲突")
    print(f"  ✓ 更好的并发性能")
    print(f"  ✓ 更简洁的代码结构")
    print(f"  ✓ 原生异步支持")
    print(f"  ✓ 更好的资源管理")


if __name__ == "__main__":
    asyncio.run(main())
