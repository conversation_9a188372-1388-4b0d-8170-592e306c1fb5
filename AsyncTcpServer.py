#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步TCP服务器
使用asyncio和异步浏览器管理器
"""

import asyncio
import json
import logging
from typing import Dict, Set
from AsyncBrowserManager import AsyncBrowserManager
from Result import Result
import Crawler


class AsyncTCPServer:
    """异步TCP服务器"""
    
    def __init__(self, logger, iniconfig, host: str = '0.0.0.0', port: int = 8888):
        self.host = host
        self.port = port
        self.logger = logger
        self.config = iniconfig
        self.running = False
        self.clients: Set[asyncio.StreamWriter] = set()
        
        # 异步浏览器管理器
        self.browser_manager = AsyncBrowserManager(logger, iniconfig)
        
        # 爬虫实例
        self.crawler = None
    
    async def start(self):
        """启动异步服务器"""
        try:
            # 启动浏览器管理器
            browser_started = await self.browser_manager.start()
            if not browser_started:
                self.logger.error("[异步服务器] 浏览器管理器启动失败")
                return False
            
            # 启动TCP服务器
            server = await asyncio.start_server(
                self.handle_client,
                self.host,
                self.port
            )
            
            self.running = True
            self.logger.info(f"[异步服务器] 监听 {self.host}:{self.port}")
            print(f"[异步服务器] 监听 {self.host}:{self.port}")
            
            # 运行服务器
            async with server:
                await server.serve_forever()
                
        except Exception as e:
            self.logger.error(f"[异步服务器] 启动失败: {e}")
            await self.stop()
    
    async def handle_client(self, reader: asyncio.StreamReader, writer: asyncio.StreamWriter):
        """处理客户端连接"""
        addr = writer.get_extra_info('peername')
        self.clients.add(writer)
        
        self.logger.info(f"[异步服务器] 客户端 {addr} 已连接")
        print(f"[异步服务器] 客户端 {addr} 已连接")
        
        try:
            while self.running:
                # 读取数据
                data = await reader.read(1024)
                if not data:
                    break
                
                message = data.decode('utf-8')
                self.logger.info(f"[异步服务器] 收到来自 {addr}: {message}")
                print(f"[异步服务器] 收到来自 {addr}: {message}")
                
                # 处理命令
                response = await self.handle_command(message)
                
                # 发送响应
                if isinstance(response, str):
                    response_data = response
                elif isinstance(response, dict):
                    response_data = json.dumps(response)
                elif hasattr(response, 'to_json'):
                    response_data = response.to_json()
                else:
                    response_data = str(response)
                
                writer.write(response_data.encode('utf-8'))
                await writer.drain()
                
                self.logger.info(f"[异步服务器] 发送给 {addr}: {response_data}")
                print(f"[异步服务器] 发送给 {addr}: {response_data}")
                
        except asyncio.CancelledError:
            pass
        except Exception as e:
            self.logger.error(f"[异步服务器] 处理客户端 {addr} 时出错: {e}")
            error_response = Result.error(msg=str(e)).to_json()
            try:
                writer.write(error_response.encode('utf-8'))
                await writer.drain()
            except:
                pass
        finally:
            self.clients.discard(writer)
            writer.close()
            await writer.wait_closed()
            self.logger.info(f"[异步服务器] 客户端 {addr} 已断开")
            print(f"[异步服务器] 客户端 {addr} 已断开")
    
    async def handle_command(self, data: str):
        """处理命令"""
        try:
            # 解析JSON数据
            json_data = json.loads(data)
            command = json_data.get("command")
            config_variant = json_data.get("ConfigVariant", {})
            
            self.logger.info(f"[异步服务器] 处理命令: {command}")
            
            # 浏览器页面命令
            if command == "wifi_page":
                success = await self.browser_manager.goto_page('wifi')
                if success:
                    return Result.success(
                        value={"wifi_page": "浏览器成功打开wifi页面"}, 
                        configVariant=config_variant
                    )
                else:
                    return Result.error(msg="浏览器打开wifi页面失败")
            
            elif command == "system_page":
                success = await self.browser_manager.goto_page('system')
                if success:
                    return Result.success(
                        value={"system_page": "浏览器成功打开系统信息页面"}, 
                        configVariant=config_variant
                    )
                else:
                    return Result.error(msg="浏览器打开系统信息页面失败")
            
            elif command == "preview_page":
                success = await self.browser_manager.goto_page('preview')
                if success:
                    return Result.success(
                        value={"preview_page": "浏览器成功打开图片预览页面"}, 
                        configVariant=config_variant
                    )
                else:
                    return Result.error(msg="浏览器打开图片预览页面失败")
            
            # 窗口控制命令
            elif command == "show_browser":
                success = await self.browser_manager.show_window()
                if success:
                    return Result.success(
                        value={"show_browser": "浏览器窗口已显示"}, 
                        configVariant=config_variant
                    )
                else:
                    return Result.error(msg="显示浏览器窗口失败")
            
            elif command == "hide_browser":
                success = await self.browser_manager.hide_window()
                if success:
                    return Result.success(
                        value={"hide_browser": "浏览器窗口已隐藏"}, 
                        configVariant=config_variant
                    )
                else:
                    return Result.error(msg="隐藏浏览器窗口失败")
            
            elif command == "set_auto_hide":
                delay_seconds = 30  # 默认30秒
                if "delay" in config_variant:
                    try:
                        delay_seconds = int(config_variant["delay"])
                    except ValueError:
                        return Result.error(msg="延迟时间参数无效")
                
                await self.browser_manager.start_auto_hide_timer(delay_seconds)
                return Result.success(
                    value={"set_auto_hide": f"自动隐藏定时器已设置，{delay_seconds}秒后隐藏"}, 
                    configVariant=config_variant
                )
            
            elif command == "cancel_auto_hide":
                await self.browser_manager.cancel_auto_hide_timer()
                return Result.success(
                    value={"cancel_auto_hide": "自动隐藏定时器已取消"}, 
                    configVariant=config_variant
                )
            
            elif command == "close_browser":
                await self.browser_manager.close()
                return Result.success(
                    value={"close_browser": "浏览器已关闭"}, 
                    configVariant=config_variant
                )
            
            # 其他爬虫命令 - 使用同步爬虫
            else:
                # 对于其他命令，使用原有的爬虫逻辑
                return await self.handle_crawler_command(command, config_variant)
                
        except json.JSONDecodeError as e:
            self.logger.error(f"[异步服务器] JSON解析错误: {e}")
            return Result.error(msg="传递的JSON格式有误")
        except Exception as e:
            self.logger.error(f"[异步服务器] 命令处理错误: {e}")
            return Result.error(msg=str(e))
    
    async def handle_crawler_command(self, command: str, config_variant: dict):
        """处理爬虫命令（在线程池中运行同步代码）"""
        try:
            # 在线程池中运行同步爬虫代码
            loop = asyncio.get_event_loop()
            
            def run_crawler():
                if self.crawler is None:
                    self.crawler = Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger)
                
                # 这里可以添加其他爬虫命令的处理逻辑
                # 例如系统信息、网络信息等
                if command == "Imei":
                    return self.crawler.get_sys_info(command=command, configVariant=config_variant)
                # ... 其他命令
                
                return Result.error(msg=f"未能识别你的指令：{command}")
            
            result = await loop.run_in_executor(None, run_crawler)
            return result
            
        except Exception as e:
            self.logger.error(f"[异步服务器] 爬虫命令处理错误: {e}")
            return Result.error(msg=str(e))
    
    async def stop(self):
        """停止服务器"""
        try:
            self.running = False
            
            # 关闭所有客户端连接
            for writer in self.clients.copy():
                writer.close()
                await writer.wait_closed()
            
            # 关闭浏览器管理器
            await self.browser_manager.close()
            
            self.logger.info("[异步服务器] 已停止")
            print("[异步服务器] 已停止")
            
        except Exception as e:
            self.logger.error(f"[异步服务器] 停止时出错: {e}")


async def main():
    """主函数"""
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s[%(levelname)s]:%(name)s:%(funcName)s: %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    # 创建配置对象（这里需要根据你的实际配置类调整）
    class MockConfig:
        def get(self, section, key, default=None, auto_type=True):
            # 模拟配置，实际使用时替换为真实配置
            if section == "CrawlerConfing" and key == "BaseUrl":
                return "http://**************"
            elif section == "CrawlerConfing" and key == "PassWord":
                return "111111"
            return default
    
    config = MockConfig()
    
    # 创建并启动服务器
    server = AsyncTCPServer(logger, config, host='127.0.0.1', port=8889)
    
    try:
        await server.start()
    except KeyboardInterrupt:
        print("\n收到中断信号，正在停止服务器...")
        await server.stop()


if __name__ == '__main__':
    asyncio.run(main())
