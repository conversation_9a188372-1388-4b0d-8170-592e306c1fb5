#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器优化测试脚本
测试共享浏览器实例的性能提升
"""

import socket
import json
import time
import threading

def send_command(command, config_variant=None):
    """发送命令到TCP服务器"""
    try:
        client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        client_socket.connect(('localhost', 8888))
        
        data = {
            "command": command,
            "ConfigVariant": config_variant or {}
        }
        
        start_time = time.time()
        client_socket.send(json.dumps(data).encode('utf-8'))
        response = client_socket.recv(1024).decode('utf-8')
        end_time = time.time()
        
        client_socket.close()
        
        print(f"命令: {command}")
        print(f"响应时间: {end_time - start_time:.3f}秒")
        print(f"响应: {response}")
        print("-" * 50)
        
        return end_time - start_time
        
    except Exception as e:
        print(f"发送命令失败: {e}")
        return None

def test_browser_performance():
    """测试浏览器性能"""
    print("=== 浏览器性能测试 ===")
    print("测试优化后的浏览器启动性能")
    print()

    # 测试命令列表
    commands = ['wifi_page', 'system_page', 'preview_page']

    # 连续测试
    print("连续测试浏览器命令响应时间:")
    all_times = []
    for i, cmd in enumerate(commands * 3):  # 重复3次
        print(f"\n第{i+1}次测试: {cmd}")
        response_time = send_command(cmd)
        if response_time:
            all_times.append(response_time)
        time.sleep(1)  # 等待1秒

    # 统计结果
    print("\n=== 性能统计 ===")
    if all_times:
        avg_time = sum(all_times)/len(all_times)
        min_time = min(all_times)
        max_time = max(all_times)
        print(f"平均响应时间: {avg_time:.3f}秒")
        print(f"最快响应时间: {min_time:.3f}秒")
        print(f"最慢响应时间: {max_time:.3f}秒")
        print(f"总测试次数: {len(all_times)}")

        # 判断性能
        if avg_time < 1.0:
            print("✓ 性能优秀: 平均响应时间小于1秒")
        elif avg_time < 2.0:
            print("✓ 性能良好: 平均响应时间小于2秒")
        else:
            print("⚠ 性能一般: 平均响应时间超过2秒")

def test_browser_status():
    """测试浏览器状态查询"""
    print("\n=== 浏览器状态测试 ===")
    send_command("browser_status")

def test_browser_close():
    """测试浏览器关闭"""
    print("\n=== 浏览器关闭测试 ===")
    send_command("close_browser")

if __name__ == "__main__":
    print("浏览器优化测试工具")
    print("请确保TCP服务器已启动 (端口8888)")
    print()
    
    try:
        # 测试连接
        test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        test_socket.connect(('localhost', 8888))
        test_socket.close()
        print("✓ 服务器连接正常")
        print()
        
        # 运行性能测试
        test_browser_performance()
        
        # 测试状态查询
        test_browser_status()
        
        # 测试关闭功能
        test_browser_close()
        
    except ConnectionRefusedError:
        print("✗ 无法连接到服务器，请确保TCP服务器已启动")
    except Exception as e:
        print(f"✗ 测试失败: {e}")
