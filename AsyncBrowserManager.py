#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步浏览器管理器
使用Playwright异步API实现浏览器窗口控制
"""

import asyncio
import logging
import platform
import subprocess
from typing import Optional, Dict
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, Page, BrowserContext


class AsyncBrowserManager:
    """异步浏览器管理器"""
    
    def __init__(self, logger: logging.Logger, config):
        self.logger = logger
        self.config = config
        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.browser_ready = False
        self.browser_visible = False
        self.auto_hide_task: Optional[asyncio.Task] = None
        
        # 页面映射
        self.page_map = {
            'wifi': 'https://www.baidu.com',
            'system': 'http://172.26.9.60/admin',
            'preview': 'https://chat.baidu.com/search'
        }
        
        # 事件循环
        self.loop = None
        self.running = False
    
    async def start(self):
        """启动异步浏览器"""
        try:
            self.logger.info("[异步浏览器] 开始初始化...")
            
            # 启动playwright
            self.playwright = await async_playwright().start()
            
            # 启动浏览器（最小化）
            self.browser = await self.playwright.chromium.launch(
                headless=False,
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--start-minimized'  # 启动时最小化
                ]
            )
            
            # 创建浏览器上下文
            self.context = await self.browser.new_context()
            
            # 创建页面
            self.page = await self.context.new_page()
            await self.page.set_default_timeout(30000)
            
            self.browser_ready = True
            self.browser_visible = False
            self.running = True
            
            self.logger.info("[异步浏览器] 初始化成功（已最小化）")
            return True
            
        except Exception as e:
            self.logger.error(f"[异步浏览器] 初始化失败: {e}")
            await self.cleanup()
            return False
    
    async def goto_page(self, page_name: str) -> bool:
        """跳转到指定页面"""
        try:
            if not self.browser_ready:
                self.logger.error("[异步浏览器] 浏览器未就绪")
                return False
            
            # 获取目标URL
            target_url = self.page_map.get(page_name)
            if not target_url:
                self.logger.error(f"[异步浏览器] 未找到页面映射: {page_name}")
                return False
            
            # 显示浏览器窗口
            if not self.browser_visible:
                await self.show_window()
            
            # 跳转页面
            self.logger.info(f"[异步浏览器] 跳转到: {target_url}")
            await self.page.goto(target_url)
            await self.page.wait_for_load_state('networkidle', timeout=10000)
            
            # 启动自动隐藏定时器
            await self.start_auto_hide_timer()
            
            self.logger.info(f"[异步浏览器] 页面跳转成功: {page_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"[异步浏览器] 页面跳转失败: {e}")
            return False
    
    async def show_window(self) -> bool:
        """显示浏览器窗口"""
        try:
            if not self.browser_ready:
                self.logger.error("[异步浏览器] 浏览器未就绪")
                return False
            
            # 取消自动隐藏
            await self.cancel_auto_hide_timer()
            
            # 根据操作系统显示窗口
            if platform.system() == "Windows":
                await self._show_window_windows()
            else:
                await self._show_window_linux()
            
            self.browser_visible = True
            self.logger.info("[异步浏览器] 窗口已显示")
            return True
            
        except Exception as e:
            self.logger.error(f"[异步浏览器] 显示窗口失败: {e}")
            return False
    
    async def hide_window(self) -> bool:
        """隐藏浏览器窗口"""
        try:
            if not self.browser_ready:
                self.logger.error("[异步浏览器] 浏览器未就绪")
                return False
            
            # 取消自动隐藏
            await self.cancel_auto_hide_timer()
            
            # 根据操作系统隐藏窗口
            if platform.system() == "Windows":
                await self._hide_window_windows()
            else:
                await self._hide_window_linux()
            
            self.browser_visible = False
            self.logger.info("[异步浏览器] 窗口已隐藏")
            return True
            
        except Exception as e:
            self.logger.error(f"[异步浏览器] 隐藏窗口失败: {e}")
            return False
    
    async def start_auto_hide_timer(self, delay_seconds: int = 30):
        """启动自动隐藏定时器"""
        try:
            # 取消之前的定时器
            await self.cancel_auto_hide_timer()
            
            # 创建新的定时器任务
            self.auto_hide_task = asyncio.create_task(
                self._auto_hide_after_delay(delay_seconds)
            )
            
            self.logger.info(f"[异步浏览器] 自动隐藏定时器已启动，{delay_seconds}秒后隐藏")
            
        except Exception as e:
            self.logger.error(f"[异步浏览器] 启动自动隐藏定时器失败: {e}")
    
    async def cancel_auto_hide_timer(self):
        """取消自动隐藏定时器"""
        try:
            if self.auto_hide_task and not self.auto_hide_task.done():
                self.auto_hide_task.cancel()
                try:
                    await self.auto_hide_task
                except asyncio.CancelledError:
                    pass
                self.logger.info("[异步浏览器] 自动隐藏定时器已取消")
        except Exception as e:
            self.logger.error(f"[异步浏览器] 取消自动隐藏定时器失败: {e}")
    
    async def _auto_hide_after_delay(self, delay_seconds: int):
        """延迟后自动隐藏"""
        try:
            await asyncio.sleep(delay_seconds)
            await self.hide_window()
            self.logger.info("[异步浏览器] 自动隐藏执行完成")
        except asyncio.CancelledError:
            self.logger.info("[异步浏览器] 自动隐藏被取消")
        except Exception as e:
            self.logger.error(f"[异步浏览器] 自动隐藏执行失败: {e}")
    
    async def _show_window_windows(self):
        """Windows系统显示窗口"""
        try:
            show_cmd = '''
            Add-Type -TypeDefinition "
                using System;
                using System.Runtime.InteropServices;
                public class Win32 {
                    [DllImport(\\"user32.dll\\")]
                    public static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);
                    [DllImport(\\"user32.dll\\")]
                    public static extern bool SetForegroundWindow(IntPtr hWnd);
                }
            "
            $chromeWindows = Get-Process chrome -ErrorAction SilentlyContinue | Where-Object {$_.MainWindowTitle -ne ""}
            foreach($window in $chromeWindows) {
                [Win32]::ShowWindow($window.MainWindowHandle, 9)  # SW_RESTORE
                [Win32]::SetForegroundWindow($window.MainWindowHandle)
            }
            '''
            
            # 异步执行PowerShell命令
            process = await asyncio.create_subprocess_exec(
                'powershell', '-Command', show_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await process.communicate()
            
        except Exception as e:
            self.logger.error(f"[异步浏览器] Windows显示窗口失败: {e}")
    
    async def _hide_window_windows(self):
        """Windows系统隐藏窗口"""
        try:
            hide_cmd = '''
            Add-Type -TypeDefinition "
                using System;
                using System.Runtime.InteropServices;
                public class Win32 {
                    [DllImport(\\"user32.dll\\")]
                    public static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);
                }
            "
            $chromeWindows = Get-Process chrome -ErrorAction SilentlyContinue | Where-Object {$_.MainWindowTitle -ne ""}
            foreach($window in $chromeWindows) {
                [Win32]::ShowWindow($window.MainWindowHandle, 6)  # SW_MINIMIZE
            }
            '''
            
            # 异步执行PowerShell命令
            process = await asyncio.create_subprocess_exec(
                'powershell', '-Command', hide_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await process.communicate()
            
        except Exception as e:
            self.logger.error(f"[异步浏览器] Windows隐藏窗口失败: {e}")
    
    async def _show_window_linux(self):
        """Linux系统显示窗口"""
        try:
            process = await asyncio.create_subprocess_exec(
                'wmctrl', '-a', 'Chrome',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await process.communicate()
        except Exception as e:
            self.logger.error(f"[异步浏览器] Linux显示窗口失败: {e}")
    
    async def _hide_window_linux(self):
        """Linux系统隐藏窗口"""
        try:
            process = await asyncio.create_subprocess_exec(
                'wmctrl', '-r', 'Chrome', '-b', 'add,hidden',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await process.communicate()
        except Exception as e:
            self.logger.error(f"[异步浏览器] Linux隐藏窗口失败: {e}")
    
    async def close(self):
        """关闭浏览器"""
        try:
            self.running = False
            
            # 取消自动隐藏定时器
            await self.cancel_auto_hide_timer()
            
            # 关闭浏览器资源
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
            
            self.browser_ready = False
            self.logger.info("[异步浏览器] 已关闭")
            
        except Exception as e:
            self.logger.error(f"[异步浏览器] 关闭失败: {e}")
    
    async def cleanup(self):
        """清理资源"""
        await self.close()
